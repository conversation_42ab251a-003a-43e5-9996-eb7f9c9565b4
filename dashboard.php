<?php
/**
 * User Dashboard for Recite! App
 * 4-Quadrant Interactive Dashboard as per PRD specifications
 */

$page_title = 'Dashboard';
require_once 'config/database.php';
require_once 'components/user_header.php';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Get user's wallet and points
$conn = getConnection();

// Fetch available content (Surahs)
$contentQuery = "SELECT * FROM content ORDER BY surah_number ASC";
$contentResult = $conn->query($contentQuery);
$availableContent = [];
if ($contentResult) {
    while ($row = $contentResult->fetch_assoc()) {
        $availableContent[] = $row;
    }
}

// Fetch user's unlocked content
$unlockedQuery = "SELECT content_id FROM unlocked_content WHERE user_id = ?";
$stmt = $conn->prepare($unlockedQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$unlockedResult = $stmt->get_result();
$unlockedContent = [];
while ($row = $unlockedResult->fetch_assoc()) {
    $unlockedContent[] = $row['content_id'];
}

// Get user rankings
$rankingsQuery = "
    SELECT
        (SELECT COUNT(*) + 1 FROM users u2 WHERE u2.points_balance > u1.points_balance AND u2.ward = u1.ward) as ward_rank,
        (SELECT COUNT(*) + 1 FROM users u2 WHERE u2.points_balance > u1.points_balance AND u2.lga = u1.lga) as lga_rank,
        (SELECT COUNT(*) + 1 FROM users u2 WHERE u2.points_balance > u1.points_balance AND u2.state = u1.state) as state_rank,
        (SELECT COUNT(*) + 1 FROM users u2 WHERE u2.points_balance > u1.points_balance) as country_rank
    FROM users u1 WHERE u1.id = ?
";
$stmt = $conn->prepare($rankingsQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$rankings = $stmt->get_result()->fetch_assoc();
?>

<style>
/* ===== COMPLETELY FRESH MOBILE-FIRST DESIGN ===== */

/* Reset Everything */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Modern CSS Variables */
:root {
    --primary: #B10020;
    --primary-dark: #8B0000;
    --white: #FFFFFF;
    --gray-50: #F9FAFB;
    --gray-100: #F3F4F6;
    --gray-200: #E5E7EB;
    --gray-300: #D1D5DB;
    --gray-400: #9CA3AF;
    --gray-500: #6B7280;
    --gray-600: #4B5563;
    --gray-700: #374151;
    --gray-800: #1F2937;
    --gray-900: #111827;
    --success: #10B981;
    --danger: #EF4444;
    --warning: #F59E0B;
    --info: #3B82F6;
    --radius: 12px;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Base Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--gray-50);
    color: var(--gray-800);
    line-height: 1.5;
}

/* Main Dashboard Container */
.dashboard-main {
    width: 100%;
    min-height: 100vh;
    background: var(--gray-50);
    padding: 1rem;
}

/* Mobile-First Grid Container */
.grid-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    max-width: 100%;
    margin: 0 auto;
}

/* Card Base Styles */
.dashboard-card {
    background: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    border: 1px solid var(--gray-200);
    min-height: 320px;
    display: flex;
    flex-direction: column;
}

.dashboard-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

/* Card Headers */
.card-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--gray-200);
}

.card-icon {
    width: 48px;
    height: 48px;
    background: var(--primary);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 0;
}

/* Card Content */
.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Content Library Styles */
.content-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 200px;
    overflow-y: auto;
}

.content-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.content-item:hover {
    background: var(--white);
    border-color: var(--primary);
    box-shadow: var(--shadow);
}

.content-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-800);
}

.content-info small {
    color: var(--primary);
    font-weight: 500;
}

/* Action Buttons */
.action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-unlock {
    background: var(--warning);
    color: var(--gray-800);
}

.btn-unlock:hover {
    background: #F59E0B;
    transform: translateY(-1px);
}

.btn-start {
    background: var(--success);
    color: var(--white);
}

.btn-start:hover {
    background: #059669;
    transform: translateY(-1px);
}

/* Video Player */
.video-container {
    flex: 1;
    background: var(--gray-900);
    border-radius: 8px;
    overflow: hidden;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-container iframe {
    width: 100%;
    height: 100%;
    border: none;
    min-height: 200px;
}

.video-placeholder {
    color: var(--gray-400);
    font-style: italic;
    text-align: center;
}

/* Recitation Engine Styles */
.recitation-display {
    flex: 1;
    background: var(--gray-100);
    border: 2px dashed var(--gray-300);
    border-radius: 8px;
    padding: 1.5rem;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Amiri', serif;
    font-size: 1.5rem;
    line-height: 2;
    text-align: center;
    direction: rtl;
}

.arabic-word {
    padding: 4px 8px;
    border-radius: 4px;
    margin: 0 4px;
    transition: all 0.3s ease;
}

.arabic-word.correct {
    background: var(--success);
    color: var(--white);
}

.arabic-word.error {
    background: var(--danger);
    color: var(--white);
}

.arabic-word.current {
    background: var(--warning);
    color: var(--gray-800);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.recitation-status {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    margin-bottom: 1rem;
    background: var(--gray-200);
    color: var(--gray-700);
}

.status-listening {
    background: var(--success);
    color: var(--white);
    animation: pulse 2s infinite;
}

.status-recording {
    background: var(--danger);
    color: var(--white);
    animation: pulse 2s infinite;
}

.recitation-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 1rem 0;
}

.control-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-start-recite {
    background: var(--success);
    color: var(--white);
}

.btn-start-recite:hover:not(:disabled) {
    background: #059669;
    transform: translateY(-2px);
}

.btn-start-recite:disabled {
    background: var(--gray-400);
    color: var(--gray-600);
    cursor: not-allowed;
}

.btn-stop-recite {
    background: var(--danger);
    color: var(--white);
}

.btn-stop-recite:hover {
    background: #DC2626;
    transform: translateY(-2px);
}

/* Selfie Mirror */
.selfie-container {
    flex: 1;
    background: var(--gray-900);
    border-radius: 8px;
    overflow: hidden;
    min-height: 200px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.selfie-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transform: scaleX(-1);
}

.camera-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    text-align: center;
    padding: 2rem;
}

.camera-btn {
    padding: 0.75rem 1.5rem;
    background: var(--primary);
    color: var(--white);
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.camera-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

/* Rankings & Stats */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-item {
    background: var(--gray-100);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
}

.rankings-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.ranking-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.ranking-item:hover {
    background: var(--white);
    border-color: var(--primary);
    box-shadow: var(--shadow);
}

.rank-label {
    font-weight: 600;
    color: var(--gray-700);
}

.rank-value {
    font-weight: 700;
    color: var(--primary);
    font-size: 1.1rem;
}

/* Responsive Design */
@media (min-width: 768px) {
    .grid-container {
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .dashboard-card {
        min-height: 400px;
    }
}

@media (min-width: 1024px) {
    .grid-container {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr 1fr;
        gap: 2rem;
        height: calc(100vh - 120px);
        max-width: 1400px;
    }

    .dashboard-card {
        min-height: auto;
        height: 100%;
    }
}

@media (min-width: 1200px) {
    .dashboard-main {
        padding: 2rem;
    }

    .grid-container {
        gap: 2.5rem;
    }
}













</style>

<!-- FRESH MOBILE-FIRST DASHBOARD -->
<div class="dashboard-main">
    <div class="grid-container">

        <!-- Quadrant 1: Content Library & Video Player -->
        <div class="dashboard-card" id="content-card">
            <div class="card-header">
                <div class="card-icon">📚</div>
                <h2 class="card-title">Content Library & Video Player</h2>
            </div>
            <div class="card-content">
                <div class="content-list">
                    <?php if (empty($availableContent)): ?>
                        <p style="text-align: center; color: var(--gray-500); font-style: italic;">No content available. Please contact admin.</p>
                    <?php else: ?>
                        <?php foreach ($availableContent as $content): ?>
                            <div class="content-item" data-content-id="<?php echo $content['id']; ?>">
                                <div class="content-info">
                                    <h4><?php echo htmlspecialchars($content['surah_name']); ?></h4>
                                    <small>Surah <?php echo $content['surah_number']; ?></small>
                                </div>
                                <?php if (in_array($content['id'], $unlockedContent)): ?>
                                    <button class="action-btn btn-start" onclick="startRecitation(<?php echo $content['id']; ?>, '<?php echo $content['youtube_id']; ?>', '<?php echo htmlspecialchars($content['arabic_text']); ?>')">
                                        Start
                                    </button>
                                <?php else: ?>
                                    <button class="action-btn btn-unlock" onclick="unlockContent(<?php echo $content['id']; ?>)">
                                        ₦<?php echo number_format($content['unlock_price']); ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <div class="video-container" id="video-container" style="display: none;">
                    <iframe id="youtube-player" src="" allowfullscreen></iframe>
                </div>
            </div>
        </div>

        <!-- Quadrant 2: Recitation Engine -->
        <div class="dashboard-card" id="recitation-card">
            <div class="card-header">
                <div class="card-icon">🎤</div>
                <h2 class="card-title">Recitation Engine</h2>
            </div>
            <div class="card-content">
                <div class="recitation-status" id="recitation-status">
                    Ready to start recitation
                </div>

                <div class="recitation-display" id="recitation-display">
                    <p style="text-align: center; color: var(--gray-500); font-style: italic;">
                        Select a Surah from the Content Library to begin recitation practice
                    </p>
                </div>

                <div class="recitation-controls">
                    <button class="control-btn btn-start-recite" id="start-recitation-btn" onclick="startRecitationEngine()" disabled>
                        🎤 Start Reciting
                    </button>
                    <button class="control-btn btn-stop-recite" id="stop-recitation-btn" onclick="stopRecitationEngine()" style="display: none;">
                        ⏹️ Stop
                    </button>
                </div>

                <div class="progress-stats" id="recitation-progress" style="display: none;">
                    <div class="stat-row">
                        <span class="stat-label">Score:</span>
                        <span class="stat-value" id="current-score">100</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Words Correct:</span>
                        <span class="stat-value" id="words-correct">0</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Current Word:</span>
                        <span class="stat-value" id="current-word-index">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quadrant 3: Selfie Mirror -->
        <div class="dashboard-card" id="selfie-card">
            <div class="card-header">
                <div class="card-icon">📷</div>
                <h2 class="card-title">Selfie Mirror</h2>
            </div>
            <div class="card-content">
                <div class="selfie-container">
                    <video id="selfie-video" class="selfie-video" autoplay muted style="display: none;"></video>
                    <div class="camera-placeholder" id="camera-placeholder">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">📷</div>
                        <p style="margin-bottom: 0.5rem;">Camera not active</p>
                        <button class="camera-btn" onclick="startCamera()">Start Camera</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quadrant 4: Rankings & Stats -->
        <div class="dashboard-card" id="rankings-card">
            <div class="card-header">
                <div class="card-icon">🏆</div>
                <h2 class="card-title">Rankings & Stats</h2>
            </div>
            <div class="card-content">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">₦<?php echo number_format($user['wallet_balance'] ?? 0, 2); ?></div>
                        <div class="stat-label">Wallet Balance</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo number_format($user['points_balance'] ?? 0); ?></div>
                        <div class="stat-label">Points</div>
                    </div>
                </div>

                <div class="rankings-list">
                    <div class="ranking-item">
                        <span class="rank-label">Ward Rank</span>
                        <span class="rank-value">#<?php echo $rankings['ward_rank'] ?? 'N/A'; ?></span>
                    </div>
                    <div class="ranking-item">
                        <span class="rank-label">LGA Rank</span>
                        <span class="rank-value">#<?php echo $rankings['lga_rank'] ?? 'N/A'; ?></span>
                    </div>
                    <div class="ranking-item">
                        <span class="rank-label">State Rank</span>
                        <span class="rank-value">#<?php echo $rankings['state_rank'] ?? 'N/A'; ?></span>
                    </div>
                    <div class="ranking-item">
                        <span class="rank-label">Country Rank</span>
                        <span class="rank-value">#<?php echo $rankings['country_rank'] ?? 'N/A'; ?></span>
                    </div>
                </div>
            </div>
        </div>

    </div> <!-- End grid-container -->
</div> <!-- End dashboard-main -->

<script>
// 4-Quadrant Dashboard Functionality

// Global variables
let currentContentId = null;
let currentArabicText = '';
let recognition = null;
let mediaRecorder = null;
let recordedChunks = [];
let currentScore = 100;
let wordsCorrect = 0;
let currentWordIndex = 0;
let arabicWords = [];
let isRecording = false;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('4-Quadrant Dashboard initialized');

    // Check for Web Speech API support
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        console.log('Speech recognition supported');
    } else {
        console.warn('Speech recognition not supported');
        document.getElementById('start-recitation-btn').disabled = true;
        document.getElementById('start-recitation-btn').textContent = 'Speech Recognition Not Supported';
    }
});

// Quadrant 1: Content Library Functions
function unlockContent(contentId) {
    if (confirm('This will deduct ₦30 from your wallet. Continue?')) {
        fetch('api/unlock_content.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                content_id: contentId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Content unlocked successfully!');
                location.reload(); // Refresh to show updated content
            } else {
                alert('Error: ' + (data.message || 'Failed to unlock content'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Network error occurred');
        });
    }
}

function startRecitation(contentId, youtubeId, arabicText) {
    currentContentId = contentId;
    currentArabicText = arabicText;

    // Show video player
    const videoContainer = document.getElementById('video-container');
    const youtubePlayer = document.getElementById('youtube-player');

    youtubePlayer.src = `https://www.youtube.com/embed/${youtubeId}?autoplay=1`;
    videoContainer.style.display = 'block';

    // Update recitation display
    const recitationDisplay = document.getElementById('recitation-display');
    recitationDisplay.innerHTML = `<div style="direction: rtl; text-align: right;">${arabicText}</div>`;

    // Parse Arabic words
    arabicWords = arabicText.split(' ').filter(word => word.trim() !== '');

    // Enable recitation button
    document.getElementById('start-recitation-btn').disabled = false;

    // Update status
    document.getElementById('recitation-status').style.display = 'block';
    document.getElementById('recitation-status').textContent = 'Video loaded. Ready to start recitation.';
    document.getElementById('recitation-status').className = 'recitation-status status-listening';

    console.log('Recitation started for content ID:', contentId);
}

// Quadrant 2: Recitation Engine Functions
function startRecitationEngine() {
    if (!currentContentId) {
        alert('Please select a Surah first');
        return;
    }

    // Reset variables
    currentScore = 100;
    wordsCorrect = 0;
    currentWordIndex = 0;

    // Update UI
    document.getElementById('start-recitation-btn').style.display = 'none';
    document.getElementById('stop-recitation-btn').style.display = 'block';
    document.getElementById('recitation-progress').style.display = 'block';
    document.getElementById('current-score').textContent = currentScore;
    document.getElementById('words-correct').textContent = wordsCorrect;
    document.getElementById('current-word-index').textContent = currentWordIndex + 1;

    // Start speech recognition
    startSpeechRecognition();

    // Start screen recording
    startScreenRecording();

    console.log('Recitation engine started');
}

function stopRecitationEngine() {
    // Stop speech recognition
    if (recognition) {
        recognition.stop();
    }

    // Stop screen recording
    stopScreenRecording();

    // Update UI
    document.getElementById('start-recitation-btn').style.display = 'block';
    document.getElementById('stop-recitation-btn').style.display = 'none';
    document.getElementById('recitation-status').textContent = 'Recitation stopped';
    document.getElementById('recitation-status').className = 'recitation-status';

    console.log('Recitation engine stopped');
}

function startSpeechRecognition() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        alert('Speech recognition not supported in this browser');
        return;
    }

    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognition = new SpeechRecognition();

    recognition.lang = 'ar-SA'; // Arabic
    recognition.continuous = true;
    recognition.interimResults = true;

    recognition.onstart = function() {
        document.getElementById('recitation-status').textContent = 'Listening...';
        document.getElementById('recitation-status').className = 'recitation-status status-listening';
    };

    recognition.onresult = function(event) {
        let finalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
            if (event.results[i].isFinal) {
                finalTranscript += event.results[i][0].transcript;
            }
        }

        if (finalTranscript) {
            processSpokenText(finalTranscript.trim());
        }
    };

    recognition.onerror = function(event) {
        console.error('Speech recognition error:', event.error);
        document.getElementById('recitation-status').textContent = 'Error: ' + event.error;
        document.getElementById('recitation-status').className = 'recitation-status';
    };

    recognition.start();
}

// Quadrant 3: Camera Functions
function startCamera() {
    const video = document.getElementById('selfie-video');
    const placeholder = document.getElementById('camera-placeholder');

    navigator.mediaDevices.getUserMedia({ video: true, audio: false })
        .then(function(stream) {
            video.srcObject = stream;
            video.style.display = 'block';
            placeholder.style.display = 'none';
            console.log('Selfie camera started');
        })
        .catch(function(error) {
            console.error('Error accessing camera:', error);
            alert('Could not access camera. Please check permissions.');
        });
}

// Screen Recording Functions
function startScreenRecording() {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
        console.warn('Screen recording not supported');
        return;
    }

    navigator.mediaDevices.getDisplayMedia({ video: true, audio: true })
        .then(function(stream) {
            mediaRecorder = new MediaRecorder(stream);
            recordedChunks = [];

            mediaRecorder.ondataavailable = function(event) {
                if (event.data.size > 0) {
                    recordedChunks.push(event.data);
                }
            };

            mediaRecorder.onstop = function() {
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                uploadRecording(blob);
            };

            mediaRecorder.start();
            isRecording = true;

            document.getElementById('recitation-status').textContent = 'Recording...';
            document.getElementById('recitation-status').className = 'recitation-status status-recording';

            console.log('Screen recording started');
        })
        .catch(function(error) {
            console.error('Error starting screen recording:', error);
        });
}

function stopScreenRecording() {
    if (mediaRecorder && isRecording) {
        mediaRecorder.stop();
        isRecording = false;
        console.log('Screen recording stopped');
    }
}

function uploadRecording(blob) {
    const formData = new FormData();
    formData.append('recording', blob, 'recitation_' + Date.now() + '.webm');
    formData.append('content_id', currentContentId);
    formData.append('score', currentScore);

    fetch('api/upload_recording.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Recording uploaded successfully');
        } else {
            console.error('Failed to upload recording');
        }
    })
    .catch(error => {
        console.error('Error uploading recording:', error);
    });
}

// Additional utility functions
function processSpokenText(spokenText) {
    // Process the spoken Arabic text and compare with expected text
    console.log('Processing spoken text:', spokenText);

    // Simple word matching logic (can be enhanced)
    const spokenWords = spokenText.split(' ');
    let matches = 0;

    spokenWords.forEach(word => {
        if (arabicWords.includes(word.trim())) {
            matches++;
        }
    });

    // Update score and progress
    wordsCorrect = matches;
    currentScore = Math.max(0, 100 - (arabicWords.length - matches) * 5);

    // Update UI
    document.getElementById('words-correct').textContent = wordsCorrect;
    document.getElementById('current-score').textContent = currentScore;

    // Highlight words in the display
    highlightWords(spokenWords);
}

function highlightWords(spokenWords) {
    const recitationDisplay = document.getElementById('recitation-display');
    let highlightedText = currentArabicText;

    spokenWords.forEach(word => {
        if (arabicWords.includes(word.trim())) {
            highlightedText = highlightedText.replace(word, `<span class="arabic-word correct">${word}</span>`);
        }
    });

    recitationDisplay.innerHTML = `<div style="direction: rtl; text-align: right;">${highlightedText}</div>`;
}
</script>

<?php require_once 'components/user_footer.php'; ?>
