<?php
/**
 * User Dashboard for Recite! App
 * Modern, responsive design with native mobile app feel
 */

$page_title = 'Dashboard';
require_once 'config/database.php';
require_once 'components/user_header.php';

// Require login
requireLogin();

$userId = $_SESSION['user_id'];
$user = getUserById($userId);

if (!$user) {
    header('Location: logout.php');
    exit;
}

// Mock data (replace with real data fetching)
$rankings = ['ward_rank' => rand(1, 50), 'lga_rank' => rand(1, 200), 'total_score' => rand(500, 2000)];
$allContent = [['id' => 1, 'surah_name' => 'Al-Fatiha', 'arabic_text' => 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ...']];
?>

<style>
    :root {
        --primary-red: #B10020;
        --primary-red-dark: #8B0000;
        --primary-red-light: #D32F2F;
        --white: #FFFFFF;
        --dark-text: #1E1E1E;
        --subtle-gray: #F3F3F3;
        --light-gray: #E8E8E8;
        --shadow: 0 4px 20px rgba(177, 0, 32, 0.1);
        --shadow-hover: 0 8px 30px rgba(177, 0, 32, 0.15);
    }

    /* Dashboard Container - Remove top margin/padding */
    .dashboard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 1.5rem 2rem 2rem 2rem;
        width: 100%;
        box-sizing: border-box;
    }

    /* Welcome Section - Positioned right after header */
    .welcome-section {
        background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
        color: white;
        padding: 2rem;
        border-radius: 20px;
        margin: 0 0 2rem 0;
        text-align: center;
        box-shadow: var(--shadow);
    }

    .welcome-section h2 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .welcome-section p {
        font-size: 1.1rem;
        margin: 0;
        opacity: 0.9;
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: var(--white);
        padding: 1.5rem;
        border-radius: 20px;
        box-shadow: var(--shadow);
        transition: all 0.3s ease;
        text-align: center;
        border: 1px solid var(--light-gray);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-hover);
    }

    .stat-card .stat-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 1.5rem;
    }

    .stat-card .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--dark-text);
        margin-bottom: 0.5rem;
    }

    .stat-card .stat-label {
        color: #666;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Main Dashboard Grid */
    .dashboard-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .dashboard-card {
        background: var(--white);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: var(--shadow);
        transition: all 0.3s ease;
        border: 1px solid var(--light-gray);
        width: 100%;
    }

    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-hover);
    }

    .dashboard-card-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--light-gray);
    }

    .dashboard-card-header .card-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.3rem;
        flex-shrink: 0;
    }

    .dashboard-card-header h3 {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--dark-text);
        margin: 0;
    }

    /* Video & Camera Containers */
    .media-container {
        position: relative;
        width: 100%;
        padding-top: 56.25%; /* 16:9 aspect ratio */
        border-radius: 15px;
        overflow: hidden;
        background: #000;
        margin-bottom: 1rem;
    }

    .media-container iframe,
    .media-container video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 15px;
    }

    .media-container video {
        transform: scaleX(-1);
        object-fit: cover;
    }

    /* Camera Placeholder */
    .camera-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #000;
        color: white;
        border-radius: 15px;
    }

    /* Recitation Panel */
    .recitation-panel {
        background: var(--subtle-gray);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 1rem;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .arabic-text {
        font-family: 'Amiri', serif;
        font-size: 1.8rem;
        line-height: 2.2;
        text-align: center;
        direction: rtl;
        color: var(--dark-text);
        font-weight: 500;
    }

    /* Buttons */
    .btn-primary {
        background: linear-gradient(135deg, var(--primary-red), var(--primary-red-light));
        color: white;
        font-weight: 600;
        border-radius: 15px;
        padding: 1rem 2rem;
        border: none;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        width: 100%;
        box-shadow: 0 4px 15px rgba(177, 0, 32, 0.3);
        cursor: pointer;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 25px rgba(177, 0, 32, 0.4);
    }

    .btn-secondary {
        background: var(--white);
        color: var(--primary-red);
        border: 2px solid var(--primary-red);
        font-weight: 600;
        border-radius: 15px;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        width: 100%;
        cursor: pointer;
    }

    .btn-secondary:hover {
        background: var(--primary-red);
        color: white;
        transform: translateY(-2px);
    }

    /* Rankings Section */
    .ranking-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid var(--light-gray);
    }

    .ranking-item:last-child {
        border-bottom: none;
    }

    .ranking-item .rank-label {
        font-weight: 600;
        color: var(--dark-text);
    }

    .ranking-item .rank-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: var(--primary-red);
    }

    /* Quick Actions */
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .action-card {
        background: var(--white);
        border: 2px solid var(--light-gray);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        text-decoration: none;
        color: var(--dark-text);
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .action-card:hover {
        border-color: var(--primary-red);
        transform: translateY(-3px);
        box-shadow: var(--shadow-hover);
        color: var(--primary-red);
        text-decoration: none;
    }

    .action-card i {
        font-size: 2rem;
        color: var(--primary-red);
    }

    .action-card span {
        font-weight: 600;
        font-size: 0.95rem;
    }

    /* Responsive Design */
    @media (min-width: 768px) {
        .dashboard-grid {
            grid-template-columns: 2fr 1fr;
        }

        .main-dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }

        .stats-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    @media (min-width: 1024px) {
        .dashboard-grid {
            grid-template-columns: 2fr 1fr;
        }

        .main-dashboard {
            grid-template-columns: 1fr 1fr;
        }
    }

    /* Tablet Responsive */
    @media (max-width: 1024px) and (min-width: 769px) {
        .dashboard-container {
            padding: 1.25rem 1.5rem 2rem 1.5rem;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .dashboard-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 1rem;
            margin: 0;
        }

        .welcome-section {
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-radius: 15px;
        }

        .welcome-section h2 {
            font-size: 1.5rem;
        }

        .dashboard-card {
            padding: 1.5rem;
            border-radius: 15px;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .quick-actions {
            grid-template-columns: 1fr;
        }

        .main-dashboard {
            grid-template-columns: 1fr;
        }

        .dashboard-grid {
            gap: 1.5rem;
        }
    }

    @media (max-width: 480px) {
        .dashboard-container {
            padding: 0.75rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .welcome-section {
            padding: 1.25rem;
            margin-bottom: 1.25rem;
        }

        .welcome-section h2 {
            font-size: 1.25rem;
        }

        .dashboard-card {
            padding: 1.25rem;
        }

        .dashboard-grid {
            gap: 1.25rem;
        }
    }
</style>

<div class="dashboard-container">
    <!-- Welcome Section -->
    <div class="welcome-section">
        <h2>Welcome back, <?php echo htmlspecialchars($user['full_name'] ?? 'Student'); ?>!</h2>
        <p>Continue your Qur'an learning journey with us today</p>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-book-open"></i>
            </div>
            <div class="stat-number"><?php echo $user_stats['total_recitations'] ?? 0; ?></div>
            <div class="stat-label">Recitations</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-trophy"></i>
            </div>
            <div class="stat-number"><?php echo $user_stats['total_score'] ?? 0; ?></div>
            <div class="stat-label">Total Score</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-fire"></i>
            </div>
            <div class="stat-number"><?php echo $user_stats['current_streak'] ?? 0; ?></div>
            <div class="stat-label">Current Streak</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-star"></i>
            </div>
            <div class="stat-number"><?php echo $user_stats['level_reached'] ?? 1; ?></div>
            <div class="stat-label">Level</div>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Left Side - Learning Content -->
        <div class="main-dashboard">
            <!-- Video Player Card -->
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="card-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3>Learn Recitation</h3>
                </div>
                <div class="media-container">
                    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" 
                            frameborder="0" 
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                            allowfullscreen>
                    </iframe>
                </div>
                <button class="btn-secondary">
                    <i class="fas fa-play me-2"></i>Watch Learning Video
                </button>
            </div>

            <!-- Selfie Mirror Card -->
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="card-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <h3>Practice Mirror</h3>
                </div>
                <div class="media-container">
                    <div id="cameraPlaceholder" class="camera-placeholder">
                        <i class="fas fa-camera fa-3x mb-3"></i>
                        <p>Click to start camera</p>
                    </div>
                    <video id="mirrorVideo" autoplay muted style="display: none;"></video>
                </div>
                <button id="startCameraBtn" class="btn-secondary">
                    <i class="fas fa-camera me-2"></i>Start Camera
                </button>
            </div>

            <!-- Recitation Panel -->
            <div class="dashboard-card" style="grid-column: 1 / -1;">
                <div class="dashboard-card-header">
                    <div class="card-icon">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <h3>Start Recitation</h3>
                </div>
                <div class="recitation-panel">
                    <div class="arabic-text">
                        <?php echo htmlspecialchars($allContent[0]['arabic_text'] ?? 'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ'); ?>
                    </div>
                </div>
                <button id="startRecitationBtn" class="btn-primary" disabled>
                    <i class="fas fa-microphone me-2"></i>Start Reciting
                </button>
            </div>
        </div>

        <!-- Right Side - Rankings & Actions -->
        <div class="sidebar-content">
            <!-- Rankings Card -->
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="card-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3>Your Rankings</h3>
                </div>
                <div class="ranking-item">
                    <span class="rank-label">Ward Rank</span>
                    <span class="rank-value">#<?php echo $rankings['ward_rank']; ?></span>
                </div>
                <div class="ranking-item">
                    <span class="rank-label">LGA Rank</span>
                    <span class="rank-value">#<?php echo $rankings['lga_rank']; ?></span>
                </div>
                <div class="ranking-item">
                    <span class="rank-label">Total Score</span>
                    <span class="rank-value"><?php echo $rankings['total_score']; ?></span>
                </div>
                <a href="rankings.php" class="btn-secondary mt-3">
                    <i class="fas fa-chart-line me-2"></i>View Full Rankings
                </a>
            </div>

            <!-- Quick Actions -->
            <div class="dashboard-card">
                <div class="dashboard-card-header">
                    <div class="card-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3>Quick Actions</h3>
                </div>
                <div class="quick-actions">
                    <a href="community.php" class="action-card">
                        <i class="fas fa-users"></i>
                        <span>Community</span>
                    </a>
                    <a href="wallet.php" class="action-card">
                        <i class="fas fa-wallet"></i>
                        <span>Wallet</span>
                    </a>
                    <a href="profile.php" class="action-card">
                        <i class="fas fa-user"></i>
                        <span>Profile</span>
                    </a>
                    <a href="streams.php" class="action-card">
                        <i class="fas fa-video"></i>
                        <span>Streams</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Dashboard specific functionality
document.addEventListener('DOMContentLoaded', function() {
    // Camera functionality
    const startCameraBtn = document.getElementById('startCameraBtn');
    const mirrorVideo = document.getElementById('mirrorVideo');
    const cameraPlaceholder = document.getElementById('cameraPlaceholder');
    const startRecitationBtn = document.getElementById('startRecitationBtn');

    if (startCameraBtn) {
        startCameraBtn.addEventListener('click', async () => {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
                mirrorVideo.srcObject = stream;
                cameraPlaceholder.style.display = 'none';
                mirrorVideo.style.display = 'block';
                startRecitationBtn.disabled = false;
                startCameraBtn.innerHTML = '<i class="fas fa-stop me-2"></i>Stop Camera';
            } catch (err) {
                console.error("Error accessing camera: ", err);
                cameraPlaceholder.innerHTML = '<i class="fas fa-exclamation-triangle fa-3x mb-3"></i><p>Could not access camera</p>';
            }
        });
    }
});
</script>

<?php require_once 'components/user_footer.php'; ?>
