<?php
/**
 * Beautiful Login Page for Recite! App
 * Modern authentication with powerful design system
 */

// Configuration needs to be loaded first
require_once 'config.php';

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields';
    } else {
        try {
            $user = executeQuery(
                "SELECT * FROM users WHERE email = ?",
                's',
                [$email]
            )->fetch_assoc();
            
            if ($user && password_verify($password, $user['password_hash'])) {
                if (!$user['payment_verified']) {
                    $error = 'Please complete your payment to activate your account. <a href="verify_payment.php?email=' . urlencode($email) . '" class="text-decoration-none">Complete Payment</a>';
                } elseif ($user['is_blocked']) {
                    $error = 'Your account has been blocked. Please contact support.';
                } else {
                    // Successful login
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_name'] = $user['full_name'];
                    $_SESSION['is_admin'] = false;
                    
                    // Update last login
                    executeQuery(
                        "UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                        'i',
                        [$user['id']]
                    );
                    
                    header('Location: dashboard.php');
                    exit;
                }
            } else {
                $error = 'Invalid email or password';
            }
        } catch (Exception $e) {
            error_log("Login error: " . $e->getMessage());
            $error = 'Login failed. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo APP_NAME; ?></title>
    
    <!-- External CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    <link href="assets/css/design-system.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Inter', sans-serif;
        }
        
        .auth-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 900px;
            margin: 2rem auto;
        }
        
        .auth-left {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .auth-left::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }
        
        .auth-right {
            padding: 3rem;
            background: white;
        }
        
        .auth-logo {
            font-size: 3rem;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #ffd700, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: none;
        }
        
        .auth-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
        }
        
        .auth-subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 2rem;
        }
        
        .feature-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .form-control {
            border-radius: 15px;
            padding: 1rem 1.5rem;
            border: 2px solid #e9ecef;
            background: #f8f9fa;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
            background: white;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.75rem;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: 700;
            font-size: 1.1rem;
            color: white;
            width: 100%;
            margin-top: 1rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .btn-login:active {
            transform: translateY(0);
        }
        
        .social-login {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
        
        .btn-social {
            border-radius: 15px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            width: 100%;
            border: 2px solid;
            transition: all 0.3s ease;
        }
        
        .btn-google {
            color: #dd4b39;
            border-color: #dd4b39;
            background: transparent;
        }
        
        .btn-google:hover {
            background: #dd4b39;
            color: white;
        }
        
        .btn-facebook {
            color: #3b5998;
            border-color: #3b5998;
            background: transparent;
        }
        
        .btn-facebook:hover {
            background: #3b5998;
            color: white;
        }
        
        .alert {
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
            border: none;
        }
        
        .alert-danger {
            background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(200, 35, 51, 0.1));
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        .alert-success {
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.1), rgba(32, 201, 151, 0.1));
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .auth-links {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }
        
        .auth-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .auth-links a:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 20%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 20%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 30%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
        
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            border: none;
            background: none;
            color: #6c757d;
            cursor: pointer;
            padding: 0;
            font-size: 1.1rem;
        }
        
        .password-toggle:hover {
            color: #495057;
        }
        
        @media (max-width: 768px) {
            .auth-container {
                margin: 1rem;
                border-radius: 20px;
            }
            
            .auth-left {
                padding: 2rem;
                min-height: 300px;
            }
            
            .auth-right {
                padding: 2rem;
            }
            
            .auth-title {
                font-size: 2rem;
            }
            
            .auth-logo {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="auth-container">
                    <div class="row g-0">
                        <!-- Left Side - Branding -->
                        <div class="col-lg-6 auth-left">
                            <div class="floating-shapes">
                                <div class="shape"></div>
                                <div class="shape"></div>
                                <div class="shape"></div>
                            </div>
                            
                            <div class="position-relative z-index-2">
                                <div class="auth-logo">
                                    <i class="fas fa-book-quran"></i>
                                </div>
                                <h1 class="auth-title">Welcome Back!</h1>
                                <p class="auth-subtitle">Continue your Qur'an recitation journey</p>
                                
                                <div class="feature-item">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-microphone-alt fa-2x me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Voice Recognition</h6>
                                            <small>AI-powered recitation analysis</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="feature-item">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-trophy fa-2x me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Competitive Rankings</h6>
                                            <small>Track your progress locally and nationally</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="feature-item">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-users fa-2x me-3"></i>
                                        <div>
                                            <h6 class="mb-1">Community Learning</h6>
                                            <small>Learn together with other students</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Right Side - Login Form -->
                        <div class="col-lg-6 auth-right">
                            <div class="text-center mb-4">
                                <h2 class="fw-bold text-dark mb-2">Sign In</h2>
                                <p class="text-muted">Enter your credentials to access your account</p>
                            </div>
                            
                            <?php if ($error): ?>
                                <div class="alert alert-danger animate-fade-in">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    <?php echo $error; ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($success): ?>
                                <div class="alert alert-success animate-fade-in">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <?php echo $success; ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" id="loginForm">
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Email Address
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="Enter your email" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Password
                                    </label>
                                    <div class="position-relative">
                                        <input type="password" class="form-control" id="password" name="password" 
                                               placeholder="Enter your password" required>
                                        <button type="button" class="password-toggle" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="passwordIcon"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                    <label class="form-check-label" for="remember">
                                        Remember me for 30 days
                                    </label>
                                </div>
                                
                                <button type="submit" class="btn btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Sign In
                                </button>
                            </form>
                            
                            <div class="social-login">
                                <p class="text-center text-muted mb-3">Or continue with</p>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <button class="btn btn-google" onclick="googleLogin()">
                                            <i class="fab fa-google me-2"></i>Google
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button class="btn btn-facebook" onclick="facebookLogin()">
                                            <i class="fab fa-facebook me-2"></i>Facebook
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="auth-links">
                                <p class="mb-2">
                                    <a href="#" onclick="showForgotPassword()">Forgot your password?</a>
                                </p>
                                <p class="mb-0">
                                    Don't have an account? 
                                    <a href="register.php">Sign up here</a>
                                </p>
                                <hr class="my-3">
                                <p class="mb-0">
                                    <small class="text-muted">
                                        Admin access? 
                                        <a href="admin/login.php" class="text-primary">Admin Login</a>
                                    </small>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Forgot Password Modal -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">Enter your email address and we'll send you instructions to reset your password.</p>
                    <form id="forgotPasswordForm">
                        <div class="mb-3">
                            <label for="resetEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="resetEmail" placeholder="Enter your email" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="resetPassword()">
                        <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password visibility toggle
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordField.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }
        
        // Social login functions
        function googleLogin() {
            showAlert('Google OAuth integration coming soon!', 'info');
        }
        
        function facebookLogin() {
            showAlert('Facebook OAuth integration coming soon!', 'info');
        }
        
        // Forgot password
        function showForgotPassword() {
            const modal = new bootstrap.Modal(document.getElementById('forgotPasswordModal'));
            modal.show();
        }
        
        function resetPassword() {
            const email = document.getElementById('resetEmail').value;
            if (email) {
                showAlert('Password reset instructions sent to your email!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('forgotPasswordModal')).hide();
            } else {
                showAlert('Please enter your email address', 'danger');
            }
        }
        
        // Alert function
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // Form enhancement
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';
            submitBtn.disabled = true;
        });
        
        // Auto-focus first input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('email').focus();
        });
    </script>
</body>
</html> 